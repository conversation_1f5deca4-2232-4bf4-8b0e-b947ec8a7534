import { OpenAI } from 'openai'

const openai = new OpenAI({ apiKey: useRuntimeConfig().openaiApiKey })

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const prompt = body.prompt || 'Write a social media post about innovation.'
  const tone = body.tone || 'professional'

  const response = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      { role: 'system', content: `You are a helpful assistant that writes ${tone} social media posts and generates relevant hashtags.` },
      { role: 'user', content: `Generate a ${tone} social media post about: ${prompt}. Include a short post followed by a list of relevant hashtags.` }
    ],
    max_tokens: 250
  })

  const content = response.choices[0]?.message?.content || ''

  const match = content.match(/(.*)(#.+)/s)
  const text = match?.[1]?.trim() || content.trim()
  const hashtags = match?.[2]?.trim() || ''

  return { text, hashtags }
});
