<template>
    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 bg-social-pattern opacity-30"></div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-primary-200 rounded-full opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-secondary-200 rounded-full opacity-20 animate-float" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-40 left-20 w-12 h-12 bg-primary-300 rounded-full opacity-20 animate-float" style="animation-delay: 4s;"></div>
        <div class="absolute bottom-20 right-10 w-24 h-24 bg-secondary-300 rounded-full opacity-20 animate-float" style="animation-delay: 1s;"></div>

        <!-- Social Media Icons Background -->
        <div class="absolute top-32 right-1/4 text-primary-200 opacity-10 text-6xl animate-pulse-slow">📱</div>
        <div class="absolute bottom-32 left-1/4 text-secondary-200 opacity-10 text-6xl animate-pulse-slow" style="animation-delay: 1.5s;">💬</div>
        <div class="absolute top-1/2 left-10 text-primary-200 opacity-10 text-5xl animate-pulse-slow" style="animation-delay: 3s;">📸</div>
        <div class="absolute top-1/3 right-10 text-secondary-200 opacity-10 text-5xl animate-pulse-slow" style="animation-delay: 2.5s;">🚀</div>

        <!-- Main Content -->
        <div class="relative z-10 min-h-screen flex flex-col">
            <!-- Header -->
            <header class="text-center py-12">
                <div class="max-w-4xl mx-auto px-6">
                    <h1 class="text-5xl md:text-6xl font-bold mb-4 font-display bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                        ✨ Social Media Post Generator
                    </h1>
                    <p class="text-xl md:text-2xl text-secondary-600 font-medium mb-2">
                        Create engaging content in seconds
                    </p>
                    <p class="text-lg text-secondary-500 max-w-2xl mx-auto">
                        Transform your ideas into compelling social media posts with AI-powered content generation
                    </p>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 flex items-start justify-center px-6 pb-12">
                <div class="w-full max-w-2xl">
                    <NuxtPage />
                </div>
            </main>

            <!-- Footer -->
            <footer class="text-center py-8 text-secondary-400">
                <p class="text-sm">Built for creators</p>
            </footer>
        </div>
    </div>
</template>