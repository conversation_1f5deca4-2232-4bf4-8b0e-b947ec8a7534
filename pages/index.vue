<template>
    <form @submit.prevent="submitPrompt" class="space-y-6 bg-white p-6 rounded-2xl shadow-xl border border-gray-100">
      <div>
        <label class="block mb-2 font-medium text-lg">What's your post about?</label>
        <textarea
          v-model="prompt"
          class="w-full p-4 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
          rows="4"
          placeholder="Enter a topic, headline or idea..."
        ></textarea>
      </div>
  
      <div>
        <label class="block mb-2 font-medium text-lg">Choose your tone</label>
        <select v-model="tone" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
          <option value="professional">Professional</option>
          <option value="casual">Casual</option>
          <option value="witty">Witty</option>
          <option value="inspirational">Inspirational</option>
        </select>
      </div>
  
      <button
        type="submit"
        class="w-full bg-primary text-white font-semibold py-3 rounded-lg hover:bg-indigo-700 transition"
      >
        🚀 Generate Post
      </button>
  
      <div v-if="loading" class="text-center text-gray-500">Generating your content...</div>
  
      <div v-if="result" class="mt-6 bg-gray-50 p-5 rounded-xl border border-gray-200">
        <p class="text-lg font-semibold text-gray-800 mb-2">Generated Post:</p>
        <p class="text-gray-700 whitespace-pre-line">{{ result.text }}</p>
        <p v-if="result.hashtags" class="mt-4 text-sm text-primary">{{ result.hashtags }}</p>
      </div>
    </form>
  </template>
  
  <script setup lang="ts">
  const prompt = ref('')
  const tone = ref('professional')
  const result = ref<{ text: string; hashtags?: string } | null>(null)
  const loading = ref(false)
  
  const submitPrompt = async () => {
    loading.value = true
    result.value = null
    const { data } = await useFetch('/api/generate', {
      method: 'POST',
      body: { prompt: prompt.value, tone: tone.value }
    })
    result.value = data.value || { text: 'No result' }
    loading.value = false
  }
</script>