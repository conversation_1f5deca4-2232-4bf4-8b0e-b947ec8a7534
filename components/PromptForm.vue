<template>
  <form @submit.prevent="submitPrompt" class="space-y-4">
    <textarea
      v-model="prompt"
      class="w-full p-4 border rounded shadow"
      rows="4"
      placeholder="Enter a topic or headline..."
    ></textarea>
    <button
      type="submit"
      class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
    >
      Generate
    </button>
    <div v-if="loading">Generating...</div>
    <div v-if="result" class="mt-4 p-4 bg-white border rounded">
      <p>{{ result }}</p>
    </div>
  </form>
</template>

<script setup lang="ts">
const prompt = ref('')
const result = ref('')
const loading = ref(false)

const submitPrompt = async () => {
  loading.value = true
  result.value = ''
  const { data } = await useFetch('/api/generate', {
    method: 'POST',
    body: { prompt: prompt.value }
  })
  result.value = data.value?.text || 'No result'
  loading.value = false
}
</script>
